package com.midea.pam.common.mdw.entity;

import com.midea.pam.common.base.LongIdEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "")
public class DwdProjectIncomeSummary extends LongIdEntity implements Serializable {
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "业务实体id")
    private Long ouId;

    @ApiModelProperty(value = "业务实体名称")
    private String ouName;

    @ApiModelProperty(value = "使用单位ID")
    private Long parentUnitId;

    @ApiModelProperty(value = "项目业务分类ID")
    private Long unitId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "创建时间")
    private Date buildTime;

    @ApiModelProperty(value = "发起人MIP")
    private String mip;

    @ApiModelProperty(value = "发起人")
    private String mipName;

    @ApiModelProperty(value = "项目来源:1-项目立项;2-预立项转正")
    private Integer projectSource;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "汇率")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "项目总收入")
    private BigDecimal amount;

    @ApiModelProperty(value = "项目总预算")
    private BigDecimal budgetCost;

    @ApiModelProperty(value = "毛利率")
    private BigDecimal grossProfitRatio;

    @ApiModelProperty(value = "删除状态(1失效/0有效)")
    private Boolean deletedFlag;

    private static final long serialVersionUID = 1L;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getOuId() {
        return ouId;
    }

    public void setOuId(Long ouId) {
        this.ouId = ouId;
    }

    public String getOuName() {
        return ouName;
    }

    public void setOuName(String ouName) {
        this.ouName = ouName == null ? null : ouName.trim();
    }

    public Long getParentUnitId() {
        return parentUnitId;
    }

    public void setParentUnitId(Long parentUnitId) {
        this.parentUnitId = parentUnitId;
    }

    public Long getUnitId() {
        return unitId;
    }

    public void setUnitId(Long unitId) {
        this.unitId = unitId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode == null ? null : projectCode.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getBuildTime() {
        return buildTime;
    }

    public void setBuildTime(Date buildTime) {
        this.buildTime = buildTime;
    }

    public String getMip() {
        return mip;
    }

    public void setMip(String mip) {
        this.mip = mip == null ? null : mip.trim();
    }

    public String getMipName() {
        return mipName;
    }

    public void setMipName(String mipName) {
        this.mipName = mipName == null ? null : mipName.trim();
    }

    public Integer getProjectSource() {
        return projectSource;
    }

    public void setProjectSource(Integer projectSource) {
        this.projectSource = projectSource;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBudgetCost() {
        return budgetCost;
    }

    public void setBudgetCost(BigDecimal budgetCost) {
        this.budgetCost = budgetCost;
    }

    public BigDecimal getGrossProfitRatio() {
        return grossProfitRatio;
    }

    public void setGrossProfitRatio(BigDecimal grossProfitRatio) {
        this.grossProfitRatio = grossProfitRatio;
    }

    public Boolean getDeletedFlag() {
        return deletedFlag;
    }

    public void setDeletedFlag(Boolean deletedFlag) {
        this.deletedFlag = deletedFlag;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", projectId=").append(projectId);
        sb.append(", ouId=").append(ouId);
        sb.append(", ouName=").append(ouName);
        sb.append(", parentUnitId=").append(parentUnitId);
        sb.append(", unitId=").append(unitId);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", type=").append(type);
        sb.append(", version=").append(version);
        sb.append(", buildTime=").append(buildTime);
        sb.append(", mip=").append(mip);
        sb.append(", mipName=").append(mipName);
        sb.append(", projectSource=").append(projectSource);
        sb.append(", currency=").append(currency);
        sb.append(", conversionRate=").append(conversionRate);
        sb.append(", amount=").append(amount);
        sb.append(", budgetCost=").append(budgetCost);
        sb.append(", grossProfitRatio=").append(grossProfitRatio);
        sb.append(", deletedFlag=").append(deletedFlag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}