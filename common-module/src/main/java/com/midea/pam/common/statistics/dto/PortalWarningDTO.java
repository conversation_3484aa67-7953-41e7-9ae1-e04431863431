package com.midea.pam.common.statistics.dto;

import java.util.Date;

public class PortalWarningDTO {

    private Long calculateId;

    private Long projectId;

    private Long targetId;

    private String targetCode;

    private String targetName;

    private String remindTypeName;

    private String title;

    private Date remindDate;


    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public String getRemindTypeName() {
        return remindTypeName;
    }

    public void setRemindTypeName(String remindTypeName) {
        this.remindTypeName = remindTypeName;
    }

    public String getTargetCode() {
        return targetCode;
    }

    public void setTargetCode(String targetCode) {
        this.targetCode = targetCode;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getRemindDate() {
        return remindDate;
    }

    public void setRemindDate(Date remindDate) {
        this.remindDate = remindDate;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getCalculateId() {
        return calculateId;
    }

    public void setCalculateId(Long calculateId) {
        this.calculateId = calculateId;
    }

    @Override
    public String toString() {
        return "PortalWarningDTO{" +
                "calculateId=" + calculateId +
                ", projectId=" + projectId +
                ", targetId=" + targetId +
                ", targetCode='" + targetCode + '\'' +
                ", targetName='" + targetName + '\'' +
                ", remindTypeName='" + remindTypeName + '\'' +
                ", title='" + title + '\'' +
                ", remindDate=" + remindDate +
                '}';
    }
}

